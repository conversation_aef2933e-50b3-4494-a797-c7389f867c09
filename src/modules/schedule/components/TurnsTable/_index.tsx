import { Eye, Plus, Trash2 } from "lucide-react";
import { withForm } from "~/core/components/form/form";
import { defaultValues } from "../../utils/defaultValues";

const TurnsTable = withForm({
	defaultValues,
	props: {
		selectedTurnIndex: null,
		onTurnSelect: (val: number | null) => null,
	},
	render: ({ form, selectedTurnIndex, onTurnSelect }) => {
		const formatTime = (time: number) => {
			const hours = Math.floor(time / 100);
			const minutes = time % 100;
			return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
		};

		const parseTime = (timeString: string) => {
			const [hours, minutes] = timeString.split(":").map(Number);
			return (hours || 0) * 100 + (minutes || 0);
		};

		return (
			<div>
				<span className="label-text font-semibold">Turnos</span>
				<form.Field name="turns" mode="array">
					{(field) => {
						const turns = field.state.value;
						return (
							<div className="space-y-4">
								<div className="flex justify-end">
									<button
										type="button"
										className="btn btn-primary btn-sm"
										onClick={() =>
											field.pushValue({
												name: "",
												startTime: 800,
												endTime: 1700,
											})
										}
									>
										<Plus size={16} />
										Agregar Turno
									</button>
								</div>

								{turns.length > 0 && (
									<div className="overflow-x-auto">
										<table className="table-zebra table">
											<thead>
												<tr>
													<th>Nombre</th>
													<th>Hora Inicio</th>
													<th>Hora Fin</th>
													<th>Acciones</th>
												</tr>
											</thead>
											<tbody>
												{turns.map(({ name }, index) => (
													<tr
														key={index + name}
														className={
															selectedTurnIndex === index ? "bg-primary/20" : ""
														}
													>
														<td>
															<form.Field name={`turns[${index}].name`}>
																{(subField) => (
																	<input
																		type="text"
																		className="input input-sm w-full"
																		value={subField.state.value || ""}
																		onChange={(e) =>
																			subField.handleChange(e.target.value)
																		}
																		placeholder="Nombre del turno"
																	/>
																)}
															</form.Field>
														</td>
														<td>
															<form.Field name={`turns[${index}].startTime`}>
																{(subField) => (
																	<input
																		type="time"
																		className="input input-sm w-full"
																		value={formatTime(
																			subField.state.value || 800,
																		)}
																		onChange={(e) =>
																			subField.handleChange(
																				parseTime(e.target.value),
																			)
																		}
																	/>
																)}
															</form.Field>
														</td>
														<td>
															<form.Field name={`turns[${index}].endTime`}>
																{(subField) => (
																	<input
																		type="time"
																		className="input input-sm w-full"
																		value={formatTime(
																			subField.state.value || 1700,
																		)}
																		onChange={(e) =>
																			subField.handleChange(
																				parseTime(e.target.value),
																			)
																		}
																	/>
																)}
															</form.Field>
														</td>
														<td>
															<div className="flex gap-2">
																<button
																	type="button"
																	className={`btn btn-sm ${
																		selectedTurnIndex === index
																			? "btn-primary"
																			: "btn-outline"
																	}`}
																	onClick={() =>
																		onTurnSelect(
																			selectedTurnIndex === index
																				? null
																				: index,
																		)
																	}
																>
																	<Eye size={16} />
																</button>
																<button
																	type="button"
																	className="btn btn-error btn-sm"
																	onClick={() => {
																		field.removeValue(index);
																		if (selectedTurnIndex === index) {
																			onTurnSelect(null);
																		} else if (
																			selectedTurnIndex !== null &&
																			selectedTurnIndex > index
																		) {
																			onTurnSelect(selectedTurnIndex - 1);
																		}
																	}}
																>
																	<Trash2 size={16} />
																</button>
															</div>
														</td>
													</tr>
												))}
											</tbody>
										</table>
									</div>
								)}

								{turns.length === 0 && (
									<div className="card bg-base-200">
										<div className="card-body text-center">
											<p className="text-base-content/70">
												No hay turnos agregados. Haz clic en "Agregar Turno"
												para comenzar.
											</p>
										</div>
									</div>
								)}
							</div>
						);
					}}
				</form.Field>
			</div>
		);
	},
});

export default TurnsTable;
